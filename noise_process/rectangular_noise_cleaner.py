#!/usr/bin/env python3
"""
FITS文件矩形噪点清理工具
专门用于检测和清理FITS图像中9个像素以内的矩形噪点，特点是矩形排列有比较尖锐的边缘
"""

import os
import sys
import numpy as np
import logging
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm
import cv2
from scipy import ndimage
from scipy.stats import zscore
from skimage import morphology, filters, measure, feature
from astropy.io import fits
from astropy.stats import sigma_clipped_stats, mad_std
import warnings

# 忽略警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
warnings.filterwarnings('ignore', category=UserWarning)


class RectangularNoiseCleaner:
    """FITS文件矩形噪点清理器"""
    
    def __init__(self, log_level=logging.INFO):
        """
        初始化矩形噪点清理器
        
        Args:
            log_level: 日志级别
        """
        self.setup_logging(log_level)
        
        # 默认参数
        self.clean_params = {
            # 噪点检测参数
            'zscore_threshold': 5.0,        # Z-score阈值（提高以减少误检）
            'max_noise_size': 9,            # 最大噪点大小（像素数）
            'min_noise_size': 1,            # 最小噪点大小（像素数）
            'rectangularity_threshold': 0.5, # 矩形度阈值
            'edge_sharpness_threshold': 0.2, # 边缘锐度阈值
            
            # 形态学参数
            'morphology_kernel_size': 3,    # 形态学核大小
            'edge_detection_sigma': 1.0,    # 边缘检测高斯标准差
            
            # 清理参数
            'cleaning_method': 'adaptive',  # 清理方法: adaptive, median, gaussian, interpolation
            'median_kernel_size': 3,        # 中值滤波核大小
            'gaussian_sigma': 1.0,          # 高斯滤波标准差
            'interpolation_method': 'bilinear', # 插值方法
            
            # 输出参数
            'save_visualization': True,     # 保存可视化结果
            'save_mask': True,             # 保存噪点掩码
            'save_analysis': True,         # 保存分析结果
        }
        
        self.logger.info("矩形噪点清理器初始化完成")
    
    def setup_logging(self, log_level):
        """设置日志"""
        self.logger = logging.getLogger('RectangularNoiseCleaner')
        self.logger.setLevel(log_level)
        
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def load_fits_data(self, fits_path):
        """
        加载FITS文件数据
        
        Args:
            fits_path (str): FITS文件路径
            
        Returns:
            tuple: (图像数据, FITS头信息)，如果失败返回(None, None)
        """
        try:
            with fits.open(fits_path) as hdul:
                data = hdul[0].data.astype(np.float64)
                header = hdul[0].header
                
                # 处理可能的3D数据（取第一个通道）
                if len(data.shape) == 3:
                    data = data[0]
                
                self.logger.info(f"成功加载FITS文件: {os.path.basename(fits_path)}")
                self.logger.info(f"数据形状: {data.shape}, 数据范围: [{np.min(data):.6f}, {np.max(data):.6f}]")
                
                return data, header
                
        except Exception as e:
            self.logger.error(f"加载FITS文件失败 {fits_path}: {str(e)}")
            return None, None
    
    def detect_rectangular_noise(self, image_data):
        """
        检测矩形噪点
        
        Args:
            image_data (np.ndarray): 输入图像数据
            
        Returns:
            tuple: (噪点掩码, 噪点分析结果)
        """
        try:
            self.logger.info("开始检测矩形噪点...")
            
            # 1. 统计异常值检测
            outlier_mask = self._detect_statistical_outliers(image_data)
            
            # 2. 连通组件分析
            labeled_mask, num_components = self._label_connected_components(outlier_mask)
            
            # 3. 矩形噪点筛选
            rectangular_mask, analysis_results = self._filter_rectangular_noise(
                image_data, labeled_mask, num_components
            )
            
            noise_count = np.sum(rectangular_mask)
            total_pixels = image_data.size
            noise_ratio = noise_count / total_pixels * 100
            
            self.logger.info(f"检测到 {noise_count} 个矩形噪点像素 ({noise_ratio:.3f}%)")
            self.logger.info(f"检测到 {len(analysis_results)} 个矩形噪点区域")
            
            return rectangular_mask, analysis_results
            
        except Exception as e:
            self.logger.error(f"矩形噪点检测失败: {str(e)}")
            return np.zeros_like(image_data, dtype=bool), []
    
    def _detect_statistical_outliers(self, image_data):
        """使用统计方法检测异常值"""
        # 计算背景统计
        mean, median, std = sigma_clipped_stats(image_data, sigma=3.0, maxiters=5)
        mad = mad_std(image_data)
        
        # 使用MAD-based Z-score检测异常值
        if mad > 0:
            z_scores = np.abs((image_data - median) / mad)
            outlier_mask = z_scores > self.clean_params['zscore_threshold']
        else:
            # 备用方法：使用标准差
            if std > 0:
                z_scores = np.abs((image_data - mean) / std)
                outlier_mask = z_scores > self.clean_params['zscore_threshold']
            else:
                outlier_mask = np.zeros_like(image_data, dtype=bool)
        
        self.logger.info(f"统计异常值检测: {np.sum(outlier_mask)} 个像素")
        return outlier_mask
    
    def _label_connected_components(self, binary_mask):
        """标记连通组件"""
        # 使用8连通性进行标记
        labeled_mask, num_components = ndimage.label(binary_mask, structure=np.ones((3, 3)))
        
        self.logger.info(f"连通组件分析: 发现 {num_components} 个连通区域")
        return labeled_mask, num_components
    
    def _filter_rectangular_noise(self, image_data, labeled_mask, num_components):
        """筛选矩形噪点"""
        rectangular_mask = np.zeros_like(labeled_mask, dtype=bool)
        analysis_results = []

        # 如果组件数量太多，显示进度并限制处理数量
        max_components_to_process = 50000  # 限制处理的最大组件数
        if num_components > max_components_to_process:
            self.logger.warning(f"连通组件数量过多 ({num_components})，只处理前 {max_components_to_process} 个")
            num_components = max_components_to_process

        processed_count = 0
        valid_count = 0

        for component_id in range(1, num_components + 1):
            # 显示进度
            if processed_count % 10000 == 0 and processed_count > 0:
                self.logger.info(f"已处理 {processed_count}/{num_components} 个组件，找到 {valid_count} 个矩形噪点")

            component_mask = (labeled_mask == component_id)
            component_size = np.sum(component_mask)
            processed_count += 1

            # 大小过滤
            if (component_size < self.clean_params['min_noise_size'] or
                component_size > self.clean_params['max_noise_size']):
                continue

            # 分析矩形特征
            analysis = self._analyze_component_rectangularity(image_data, component_mask)

            # 检查是否满足矩形噪点条件
            if (analysis['rectangularity'] >= self.clean_params['rectangularity_threshold'] and
                analysis['edge_sharpness'] >= self.clean_params['edge_sharpness_threshold']):

                rectangular_mask |= component_mask
                analysis['component_id'] = component_id
                analysis['size'] = component_size
                analysis_results.append(analysis)
                valid_count += 1

                self.logger.debug(f"矩形噪点 {component_id}: 大小={component_size}, "
                                f"矩形度={analysis['rectangularity']:.3f}, "
                                f"边缘锐度={analysis['edge_sharpness']:.3f}")

        self.logger.info(f"处理完成：共处理 {processed_count} 个组件，找到 {valid_count} 个矩形噪点")
        return rectangular_mask, analysis_results
    
    def _analyze_component_rectangularity(self, image_data, component_mask):
        """分析组件的矩形特征"""
        # 获取组件的边界框
        coords = np.where(component_mask)
        if len(coords[0]) == 0:
            return {'rectangularity': 0.0, 'edge_sharpness': 0.0, 'bbox': None}
        
        min_row, max_row = np.min(coords[0]), np.max(coords[0])
        min_col, max_col = np.min(coords[1]), np.max(coords[1])
        
        bbox_area = (max_row - min_row + 1) * (max_col - min_col + 1)
        component_area = len(coords[0])
        
        # 计算矩形度（组件面积与边界框面积的比值）
        rectangularity = component_area / bbox_area if bbox_area > 0 else 0.0
        
        # 计算边缘锐度
        edge_sharpness = self._calculate_edge_sharpness(image_data, component_mask)
        
        return {
            'rectangularity': rectangularity,
            'edge_sharpness': edge_sharpness,
            'bbox': (min_row, max_row, min_col, max_col),
            'bbox_area': bbox_area,
            'component_area': component_area
        }
    
    def _calculate_edge_sharpness(self, image_data, component_mask):
        """计算边缘锐度"""
        try:
            # 获取组件边界
            coords = np.where(component_mask)
            if len(coords[0]) == 0:
                return 0.0
            
            # 扩展边界以包含邻域
            min_row = max(0, np.min(coords[0]) - 2)
            max_row = min(image_data.shape[0], np.max(coords[0]) + 3)
            min_col = max(0, np.min(coords[1]) - 2)
            max_col = min(image_data.shape[1], np.max(coords[1]) + 3)
            
            # 提取局部区域
            local_region = image_data[min_row:max_row, min_col:max_col]
            local_mask = component_mask[min_row:max_row, min_col:max_col]
            
            if local_region.size == 0:
                return 0.0
            
            # 计算梯度幅度
            grad_x = ndimage.sobel(local_region, axis=1)
            grad_y = ndimage.sobel(local_region, axis=0)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
            
            # 计算组件边界处的平均梯度
            # 使用形态学操作找到边界
            eroded = ndimage.binary_erosion(local_mask)
            boundary = local_mask & ~eroded
            
            if np.sum(boundary) > 0:
                edge_sharpness = np.mean(gradient_magnitude[boundary])
            else:
                edge_sharpness = 0.0
            
            return edge_sharpness

        except Exception as e:
            self.logger.warning(f"计算边缘锐度失败: {str(e)}")
            return 0.0

    def clean_rectangular_noise(self, image_data, noise_mask, analysis_results):
        """
        清理矩形噪点

        Args:
            image_data (np.ndarray): 原始图像数据
            noise_mask (np.ndarray): 噪点掩码
            analysis_results (list): 噪点分析结果

        Returns:
            np.ndarray: 清理后的图像数据
        """
        try:
            self.logger.info("开始清理矩形噪点...")

            cleaned_data = image_data.copy()
            method = self.clean_params['cleaning_method']

            if method == 'adaptive':
                cleaned_data = self._adaptive_cleaning(cleaned_data, noise_mask, analysis_results)
            elif method == 'median':
                cleaned_data = self._median_cleaning(cleaned_data, noise_mask)
            elif method == 'gaussian':
                cleaned_data = self._gaussian_cleaning(cleaned_data, noise_mask)
            elif method == 'interpolation':
                cleaned_data = self._interpolation_cleaning(cleaned_data, noise_mask)
            else:
                self.logger.warning(f"未知的清理方法: {method}，使用自适应清理")
                cleaned_data = self._adaptive_cleaning(cleaned_data, noise_mask, analysis_results)

            self.logger.info("矩形噪点清理完成")
            return cleaned_data

        except Exception as e:
            self.logger.error(f"矩形噪点清理失败: {str(e)}")
            return image_data

    def _adaptive_cleaning(self, image_data, noise_mask, analysis_results):
        """自适应清理方法"""
        cleaned_data = image_data.copy()

        for analysis in analysis_results:
            component_id = analysis['component_id']
            size = analysis['size']

            # 根据噪点大小选择不同的清理策略
            if size <= 3:
                # 小噪点：使用邻域中值
                cleaned_data = self._clean_small_noise(cleaned_data, noise_mask, analysis)
            elif size <= 6:
                # 中等噪点：使用高斯滤波
                cleaned_data = self._clean_medium_noise(cleaned_data, noise_mask, analysis)
            else:
                # 大噪点：使用插值
                cleaned_data = self._clean_large_noise(cleaned_data, noise_mask, analysis)

        return cleaned_data

    def _clean_small_noise(self, image_data, noise_mask, analysis):
        """清理小型噪点（1-3像素）"""
        bbox = analysis['bbox']
        if bbox is None:
            return image_data

        min_row, max_row, min_col, max_col = bbox

        # 扩展区域以包含邻域
        pad = 2
        region_min_row = max(0, min_row - pad)
        region_max_row = min(image_data.shape[0], max_row + pad + 1)
        region_min_col = max(0, min_col - pad)
        region_max_col = min(image_data.shape[1], max_col + pad + 1)

        # 提取局部区域
        local_region = image_data[region_min_row:region_max_row, region_min_col:region_max_col]
        local_mask = noise_mask[region_min_row:region_max_row, region_min_col:region_max_col]

        # 使用中值滤波
        filtered_region = ndimage.median_filter(local_region, size=3)

        # 只在噪点位置应用滤波结果
        cleaned_region = local_region.copy()
        cleaned_region[local_mask] = filtered_region[local_mask]

        # 将结果写回原图像
        image_data[region_min_row:region_max_row, region_min_col:region_max_col] = cleaned_region

        return image_data

    def _clean_medium_noise(self, image_data, noise_mask, analysis):
        """清理中等噪点（4-6像素）"""
        bbox = analysis['bbox']
        if bbox is None:
            return image_data

        min_row, max_row, min_col, max_col = bbox

        # 扩展区域
        pad = 3
        region_min_row = max(0, min_row - pad)
        region_max_row = min(image_data.shape[0], max_row + pad + 1)
        region_min_col = max(0, min_col - pad)
        region_max_col = min(image_data.shape[1], max_col + pad + 1)

        # 提取局部区域
        local_region = image_data[region_min_row:region_max_row, region_min_col:region_max_col]
        local_mask = noise_mask[region_min_row:region_max_row, region_min_col:region_max_col]

        # 使用高斯滤波
        sigma = self.clean_params['gaussian_sigma']
        filtered_region = ndimage.gaussian_filter(local_region, sigma=sigma)

        # 只在噪点位置应用滤波结果
        cleaned_region = local_region.copy()
        cleaned_region[local_mask] = filtered_region[local_mask]

        # 将结果写回原图像
        image_data[region_min_row:region_max_row, region_min_col:region_max_col] = cleaned_region

        return image_data

    def _clean_large_noise(self, image_data, noise_mask, analysis):
        """清理大型噪点（7-9像素）"""
        bbox = analysis['bbox']
        if bbox is None:
            return image_data

        min_row, max_row, min_col, max_col = bbox

        # 扩展区域
        pad = 4
        region_min_row = max(0, min_row - pad)
        region_max_row = min(image_data.shape[0], max_row + pad + 1)
        region_min_col = max(0, min_col - pad)
        region_max_col = min(image_data.shape[1], max_col + pad + 1)

        # 使用双线性插值修复噪点
        from scipy.interpolate import griddata

        # 获取噪点区域坐标
        noise_coords = np.where(noise_mask[region_min_row:region_max_row, region_min_col:region_max_col])
        if len(noise_coords[0]) == 0:
            return image_data

        # 获取非噪点区域的坐标和值
        local_region = image_data[region_min_row:region_max_row, region_min_col:region_max_col]
        local_mask = noise_mask[region_min_row:region_max_row, region_min_col:region_max_col]

        valid_coords = np.where(~local_mask)
        if len(valid_coords[0]) < 4:  # 需要至少4个点进行插值
            # 回退到高斯滤波
            return self._clean_medium_noise(image_data, noise_mask, analysis)

        # 执行插值
        points = np.column_stack((valid_coords[0], valid_coords[1]))
        values = local_region[valid_coords]
        xi = np.column_stack((noise_coords[0], noise_coords[1]))

        try:
            interpolated_values = griddata(points, values, xi, method='linear', fill_value=np.mean(values))

            # 将插值结果写回图像
            for i, (row, col) in enumerate(zip(noise_coords[0], noise_coords[1])):
                image_data[region_min_row + row, region_min_col + col] = interpolated_values[i]

        except Exception as e:
            self.logger.warning(f"插值失败，使用高斯滤波: {str(e)}")
            return self._clean_medium_noise(image_data, noise_mask, analysis)

        return image_data

    def _median_cleaning(self, image_data, noise_mask):
        """中值滤波清理"""
        kernel_size = self.clean_params['median_kernel_size']

        # 对整个图像应用中值滤波
        filtered_image = ndimage.median_filter(image_data, size=kernel_size)

        # 只在噪点位置使用滤波结果
        cleaned_data = image_data.copy()
        cleaned_data[noise_mask] = filtered_image[noise_mask]

        return cleaned_data

    def _gaussian_cleaning(self, image_data, noise_mask):
        """高斯滤波清理"""
        sigma = self.clean_params['gaussian_sigma']

        # 对整个图像应用高斯滤波
        filtered_image = ndimage.gaussian_filter(image_data, sigma=sigma)

        # 只在噪点位置使用滤波结果
        cleaned_data = image_data.copy()
        cleaned_data[noise_mask] = filtered_image[noise_mask]

        return cleaned_data

    def _interpolation_cleaning(self, image_data, noise_mask):
        """插值清理"""
        from scipy.interpolate import griddata

        cleaned_data = image_data.copy()
        noise_coords = np.where(noise_mask)

        if len(noise_coords[0]) == 0:
            return cleaned_data

        # 获取非噪点像素的坐标和值
        valid_coords = np.where(~noise_mask)
        if len(valid_coords[0]) < 4:
            self.logger.warning("有效像素太少，无法进行插值，使用中值滤波")
            return self._median_cleaning(image_data, noise_mask)

        # 执行插值
        points = np.column_stack((valid_coords[0], valid_coords[1]))
        values = image_data[valid_coords]
        xi = np.column_stack((noise_coords[0], noise_coords[1]))

        try:
            interpolated_values = griddata(points, values, xi, method='linear', fill_value=np.mean(values))
            cleaned_data[noise_coords] = interpolated_values
        except Exception as e:
            self.logger.warning(f"插值失败，使用中值滤波: {str(e)}")
            return self._median_cleaning(image_data, noise_mask)

        return cleaned_data

    def save_fits_file(self, data, header, output_path):
        """
        保存FITS文件

        Args:
            data (np.ndarray): 图像数据
            header: FITS头信息
            output_path (str): 输出路径
        """
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 保存FITS文件
            hdu = fits.PrimaryHDU(data=data, header=header)
            hdu.writeto(output_path, overwrite=True)

            self.logger.info(f"FITS文件已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"保存FITS文件失败: {str(e)}")

    def create_visualization(self, original_data, cleaned_data, noise_mask, analysis_results, output_path):
        """
        创建可视化对比图

        Args:
            original_data (np.ndarray): 原始图像数据
            cleaned_data (np.ndarray): 清理后图像数据
            noise_mask (np.ndarray): 噪点掩码
            analysis_results (list): 分析结果
            output_path (str): 输出路径
        """
        try:
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))

            # 计算显示范围
            vmin = np.percentile(original_data, 1)
            vmax = np.percentile(original_data, 99)

            # 原始图像
            im1 = axes[0, 0].imshow(original_data, cmap='gray', vmin=vmin, vmax=vmax)
            axes[0, 0].set_title('原始图像')
            axes[0, 0].axis('off')
            plt.colorbar(im1, ax=axes[0, 0])

            # 噪点掩码
            im2 = axes[0, 1].imshow(noise_mask, cmap='Reds', alpha=0.7)
            axes[0, 1].imshow(original_data, cmap='gray', vmin=vmin, vmax=vmax, alpha=0.3)
            axes[0, 1].set_title(f'检测到的矩形噪点 ({np.sum(noise_mask)} 个像素)')
            axes[0, 1].axis('off')

            # 清理后图像
            im3 = axes[0, 2].imshow(cleaned_data, cmap='gray', vmin=vmin, vmax=vmax)
            axes[0, 2].set_title('清理后图像')
            axes[0, 2].axis('off')
            plt.colorbar(im3, ax=axes[0, 2])

            # 差异图像
            difference = original_data - cleaned_data
            im4 = axes[1, 0].imshow(difference, cmap='RdBu_r',
                                  vmin=-np.std(difference)*3, vmax=np.std(difference)*3)
            axes[1, 0].set_title('差异图像 (原始 - 清理后)')
            axes[1, 0].axis('off')
            plt.colorbar(im4, ax=axes[1, 0])

            # 噪点大小分布
            if analysis_results:
                sizes = [result['size'] for result in analysis_results]
                axes[1, 1].hist(sizes, bins=range(1, 11), alpha=0.7, edgecolor='black')
                axes[1, 1].set_xlabel('噪点大小 (像素数)')
                axes[1, 1].set_ylabel('数量')
                axes[1, 1].set_title('噪点大小分布')
                axes[1, 1].grid(True, alpha=0.3)

            # 矩形度分布
            if analysis_results:
                rectangularities = [result['rectangularity'] for result in analysis_results]
                axes[1, 2].hist(rectangularities, bins=20, alpha=0.7, edgecolor='black')
                axes[1, 2].set_xlabel('矩形度')
                axes[1, 2].set_ylabel('数量')
                axes[1, 2].set_title('矩形度分布')
                axes[1, 2].grid(True, alpha=0.3)
                axes[1, 2].axvline(self.clean_params['rectangularity_threshold'],
                                 color='red', linestyle='--', label='阈值')
                axes[1, 2].legend()

            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.info(f"可视化结果已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"创建可视化失败: {str(e)}")

    def calculate_statistics(self, original_data, cleaned_data, noise_mask, analysis_results):
        """
        计算处理统计信息

        Args:
            original_data (np.ndarray): 原始图像数据
            cleaned_data (np.ndarray): 清理后图像数据
            noise_mask (np.ndarray): 噪点掩码
            analysis_results (list): 分析结果

        Returns:
            dict: 统计信息
        """
        try:
            # 基本统计
            noise_count = np.sum(noise_mask)
            total_pixels = original_data.size
            noise_ratio = noise_count / total_pixels * 100
            num_noise_regions = len(analysis_results)

            # 图像质量指标
            original_mean, original_median, original_std = sigma_clipped_stats(original_data, sigma=3.0)
            cleaned_mean, cleaned_median, cleaned_std = sigma_clipped_stats(cleaned_data, sigma=3.0)

            # 差异统计
            difference = original_data - cleaned_data
            max_change = np.max(np.abs(difference))
            mean_change = np.mean(np.abs(difference))

            # 噪点区域的变化
            if noise_count > 0:
                noise_original_values = original_data[noise_mask]
                noise_cleaned_values = cleaned_data[noise_mask]
                noise_change = np.mean(np.abs(noise_original_values - noise_cleaned_values))
            else:
                noise_change = 0.0

            # 矩形噪点特征统计
            if analysis_results:
                sizes = [result['size'] for result in analysis_results]
                rectangularities = [result['rectangularity'] for result in analysis_results]
                edge_sharpnesses = [result['edge_sharpness'] for result in analysis_results]

                size_stats = {
                    'min': int(np.min(sizes)),
                    'max': int(np.max(sizes)),
                    'mean': float(np.mean(sizes)),
                    'median': float(np.median(sizes))
                }

                rectangularity_stats = {
                    'min': float(np.min(rectangularities)),
                    'max': float(np.max(rectangularities)),
                    'mean': float(np.mean(rectangularities)),
                    'median': float(np.median(rectangularities))
                }

                edge_sharpness_stats = {
                    'min': float(np.min(edge_sharpnesses)),
                    'max': float(np.max(edge_sharpnesses)),
                    'mean': float(np.mean(edge_sharpnesses)),
                    'median': float(np.median(edge_sharpnesses))
                }
            else:
                size_stats = rectangularity_stats = edge_sharpness_stats = {}

            stats = {
                'noise_count': int(noise_count),
                'total_pixels': int(total_pixels),
                'noise_ratio': float(noise_ratio),
                'num_noise_regions': int(num_noise_regions),
                'original_stats': {
                    'mean': float(original_mean),
                    'median': float(original_median),
                    'std': float(original_std)
                },
                'cleaned_stats': {
                    'mean': float(cleaned_mean),
                    'median': float(cleaned_median),
                    'std': float(cleaned_std)
                },
                'changes': {
                    'max_change': float(max_change),
                    'mean_change': float(mean_change),
                    'noise_region_change': float(noise_change)
                },
                'rectangular_noise_features': {
                    'size_stats': size_stats,
                    'rectangularity_stats': rectangularity_stats,
                    'edge_sharpness_stats': edge_sharpness_stats
                }
            }

            return stats

        except Exception as e:
            self.logger.error(f"计算统计信息失败: {str(e)}")
            return {}

    def save_analysis_results(self, analysis_results, output_path):
        """保存分析结果到文本文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("矩形噪点分析结果\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"检测参数:\n")
                f.write(f"  Z-score阈值: {self.clean_params['zscore_threshold']}\n")
                f.write(f"  最大噪点大小: {self.clean_params['max_noise_size']} 像素\n")
                f.write(f"  矩形度阈值: {self.clean_params['rectangularity_threshold']}\n")
                f.write(f"  边缘锐度阈值: {self.clean_params['edge_sharpness_threshold']}\n\n")

                f.write(f"检测结果:\n")
                f.write(f"  总共检测到 {len(analysis_results)} 个矩形噪点区域\n\n")

                if analysis_results:
                    f.write("详细信息:\n")
                    f.write("-" * 80 + "\n")
                    f.write(f"{'ID':<4} {'大小':<6} {'矩形度':<8} {'边缘锐度':<10} {'边界框':<20}\n")
                    f.write("-" * 80 + "\n")

                    for i, result in enumerate(analysis_results):
                        bbox_str = f"({result['bbox'][0]}-{result['bbox'][1]}, {result['bbox'][2]}-{result['bbox'][3]})"
                        f.write(f"{i+1:<4} {result['size']:<6} {result['rectangularity']:<8.3f} "
                               f"{result['edge_sharpness']:<10.3f} {bbox_str:<20}\n")

            self.logger.info(f"分析结果已保存: {output_path}")

        except Exception as e:
            self.logger.error(f"保存分析结果失败: {str(e)}")

    def process_fits_file(self, input_path, output_dir=None):
        """
        处理单个FITS文件

        Args:
            input_path (str): 输入FITS文件路径
            output_dir (str): 输出目录，如果为None则在程序所在目录下创建与文件名相关的目录

        Returns:
            dict: 处理结果
        """
        try:
            self.logger.info(f"开始处理FITS文件: {input_path}")

            # 加载数据
            original_data, header = self.load_fits_data(input_path)
            if original_data is None:
                return {'success': False, 'error': '无法加载FITS文件'}

            # 检测矩形噪点
            noise_mask, analysis_results = self.detect_rectangular_noise(original_data)

            # 清理噪点
            cleaned_data = self.clean_rectangular_noise(original_data, noise_mask, analysis_results)

            # 计算统计信息
            stats = self.calculate_statistics(original_data, cleaned_data, noise_mask, analysis_results)

            # 准备输出路径
            input_name = Path(input_path).stem
            if output_dir is None:
                # 默认在程序文件所在目录下创建与文件名相关的目录
                program_dir = Path(__file__).parent
                output_dir = program_dir / f"rectangular_noise_cleaned_{input_name}"
                self.logger.info(f"使用默认输出目录: {output_dir}")
            else:
                output_dir = Path(output_dir)

            output_dir.mkdir(parents=True, exist_ok=True)

            # 保存清理后的FITS文件
            cleaned_fits_path = output_dir / f"{input_name}_rectangular_cleaned.fits"
            self.save_fits_file(cleaned_data, header, str(cleaned_fits_path))

            # 保存噪点掩码（如果启用）
            mask_fits_path = None
            if self.clean_params['save_mask']:
                mask_fits_path = output_dir / f"{input_name}_rectangular_noise_mask.fits"
                mask_header = header.copy()
                mask_header['COMMENT'] = 'Rectangular noise mask: 1=noise, 0=clean'
                self.save_fits_file(noise_mask.astype(np.uint8), mask_header, str(mask_fits_path))

            # 创建可视化（如果启用）
            visualization_path = None
            if self.clean_params['save_visualization']:
                visualization_path = output_dir / f"{input_name}_rectangular_noise_analysis.png"
                self.create_visualization(original_data, cleaned_data, noise_mask,
                                        analysis_results, str(visualization_path))

            # 保存分析结果（如果启用）
            analysis_path = None
            if self.clean_params['save_analysis']:
                analysis_path = output_dir / f"{input_name}_rectangular_noise_analysis.txt"
                self.save_analysis_results(analysis_results, str(analysis_path))

            result = {
                'success': True,
                'input_file': input_path,
                'cleaned_fits_file': str(cleaned_fits_path),
                'mask_fits_file': str(mask_fits_path) if mask_fits_path else None,
                'visualization_file': str(visualization_path) if visualization_path else None,
                'analysis_file': str(analysis_path) if analysis_path else None,
                'statistics': stats,
                'parameters': self.clean_params.copy(),
                'analysis_results': analysis_results
            }

            self.logger.info(f"处理完成: {input_path}")
            return result

        except Exception as e:
            self.logger.error(f"处理FITS文件失败: {str(e)}")
            return {'success': False, 'error': str(e)}


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description='FITS文件矩形噪点清理工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python rectangular_noise_cleaner.py --input noise2.fits
  python rectangular_noise_cleaner.py --input noise2.fits --output ./cleaned/
  python rectangular_noise_cleaner.py --input noise2.fits --method median --max-size 6

清理方法说明:
  adaptive     - 自适应清理（默认，根据噪点大小选择不同策略）
  median       - 中值滤波清理
  gaussian     - 高斯滤波清理
  interpolation - 插值清理

特征说明:
  矩形度: 组件面积与边界框面积的比值，1.0表示完美矩形
  边缘锐度: 噪点边界处的平均梯度幅度，值越大边缘越尖锐
        """
    )

    parser.add_argument('--input', '-i', required=True,
                       help='输入FITS文件路径')
    parser.add_argument('--output', '-o',
                       help='输出目录（默认在程序目录下创建rectangular_noise_cleaned_<文件名>目录）')
    parser.add_argument('--method', '-m', choices=['adaptive', 'median', 'gaussian', 'interpolation'],
                       default='adaptive',
                       help='清理方法（默认: adaptive）')
    parser.add_argument('--threshold', '-t', type=float, default=5.0,
                       help='Z-score阈值（默认: 5.0）')
    parser.add_argument('--max-size', type=int, default=9,
                       help='最大噪点大小（像素数，默认: 9）')
    parser.add_argument('--min-size', type=int, default=1,
                       help='最小噪点大小（像素数，默认: 1）')
    parser.add_argument('--rectangularity', type=float, default=0.5,
                       help='矩形度阈值（默认: 0.5）')
    parser.add_argument('--edge-sharpness', type=float, default=0.2,
                       help='边缘锐度阈值（默认: 0.2）')
    parser.add_argument('--no-visualization', action='store_true',
                       help='不保存可视化结果')
    parser.add_argument('--no-mask', action='store_true',
                       help='不保存噪点掩码')
    parser.add_argument('--no-analysis', action='store_true',
                       help='不保存分析结果')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')

    args = parser.parse_args()

    # 设置日志级别
    log_level = logging.DEBUG if args.verbose else logging.INFO

    print("=" * 60)
    print("FITS文件矩形噪点清理工具")
    print("=" * 60)
    print(f"输入文件: {args.input}")
    print(f"清理方法: {args.method}")
    print(f"Z-score阈值: {args.threshold}")
    print(f"噪点大小范围: {args.min_size}-{args.max_size} 像素")
    print(f"矩形度阈值: {args.rectangularity}")
    print(f"边缘锐度阈值: {args.edge_sharpness}")
    print("-" * 60)

    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        sys.exit(1)

    # 创建清理器
    cleaner = RectangularNoiseCleaner(log_level=log_level)

    # 设置参数
    cleaner.clean_params['cleaning_method'] = args.method
    cleaner.clean_params['zscore_threshold'] = args.threshold
    cleaner.clean_params['max_noise_size'] = args.max_size
    cleaner.clean_params['min_noise_size'] = args.min_size
    cleaner.clean_params['rectangularity_threshold'] = args.rectangularity
    cleaner.clean_params['edge_sharpness_threshold'] = args.edge_sharpness
    cleaner.clean_params['save_visualization'] = not args.no_visualization
    cleaner.clean_params['save_mask'] = not args.no_mask
    cleaner.clean_params['save_analysis'] = not args.no_analysis

    # 处理文件
    result = cleaner.process_fits_file(args.input, args.output)

    if result['success']:
        stats = result['statistics']
        print("\n" + "=" * 60)
        print("处理完成!")
        print("=" * 60)
        print(f"检测到矩形噪点: {stats['noise_count']} 个像素 ({stats['noise_ratio']:.3f}%)")
        print(f"矩形噪点区域: {stats['num_noise_regions']} 个")

        if stats['rectangular_noise_features']['size_stats']:
            size_stats = stats['rectangular_noise_features']['size_stats']
            print(f"噪点大小: {size_stats['min']}-{size_stats['max']} 像素 (平均: {size_stats['mean']:.1f})")

        if stats['rectangular_noise_features']['rectangularity_stats']:
            rect_stats = stats['rectangular_noise_features']['rectangularity_stats']
            print(f"矩形度: {rect_stats['min']:.3f}-{rect_stats['max']:.3f} (平均: {rect_stats['mean']:.3f})")

        if stats['rectangular_noise_features']['edge_sharpness_stats']:
            edge_stats = stats['rectangular_noise_features']['edge_sharpness_stats']
            print(f"边缘锐度: {edge_stats['min']:.3f}-{edge_stats['max']:.3f} (平均: {edge_stats['mean']:.3f})")

        print(f"\n图像变化:")
        print(f"  最大变化: {stats['changes']['max_change']:.6f}")
        print(f"  平均变化: {stats['changes']['mean_change']:.6f}")
        print(f"  噪点区域平均变化: {stats['changes']['noise_region_change']:.6f}")

        print(f"\n输出文件:")
        print(f"  清理后FITS: {os.path.basename(result['cleaned_fits_file'])}")
        if result['mask_fits_file']:
            print(f"  噪点掩码: {os.path.basename(result['mask_fits_file'])}")
        if result['visualization_file']:
            print(f"  可视化图: {os.path.basename(result['visualization_file'])}")
        if result['analysis_file']:
            print(f"  分析结果: {os.path.basename(result['analysis_file'])}")
    else:
        print(f"\n处理失败: {result.get('error', '未知错误')}")
        sys.exit(1)


if __name__ == '__main__':
    main()
